'use client'
import { DashboardDataWithChatHistory } from '@/app/type/dashboard_data'
import dayjs from 'dayjs'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { RxCross2 } from 'react-icons/rx'
import { FaPlus, FaCheck, FaTimes } from 'react-icons/fa'
import { HiPencilAlt, HiOutlinePencilAlt } from 'react-icons/hi'
import { toast } from 'react-toastify'

export function DashboardShow({
  initialDashboard,
  deleteDashboardData,
  deleteDashboardTag,
  addDashboardTag,
  updateDashboardDescription
}:{
  initialDashboard:DashboardDataWithChatHistory[]
  deleteDashboardData(id:string): Promise<void>
  deleteDashboardTag(id:string, tag:string): Promise<void>
  addDashboardTag(id:string, tag:string): Promise<void>
  updateDashboardDescription(id:string, description:string): Promise<void>
}) {
  const [dashboard, setDashboard] = useState<DashboardDataWithChatHistory[]>(initialDashboard)
  useEffect(() => {
    setDashboard(initialDashboard)
  }, [initialDashboard])
  return <div>
    <div className='flex flex-col gap-4'>
      {dashboard.map((item) => <DashboardDetail
        key={item.id}
        dashboard={item}
        deleteDashboardData={() => deleteDashboardData(item.id)}
        deleteDashboardTag={(tag) => {
          toast.promise(deleteDashboardTag(item.id, tag), {
            pending: 'delete pending',
            success: 'delete success',
            error: 'delete error'
          }).then(() => {
            setDashboard((data) => {
              return data.map((dataItem) => {
                if (item.id == dataItem.id) {
                  return {
                    ...dataItem,
                    tag: dataItem.tag.filter((dataItemTag) => dataItemTag != tag)
                  }
                } else {
                  return dataItem
                }
              })
            })
          })
        }}
        addDashboardTag={(tag) => {
          toast.promise(addDashboardTag(item.id, tag), {
            pending: 'add tag pending',
            success: 'add tag success',
            error: 'add tag error'
          }).then(() => {
            setDashboard((data) => {
              return data.map((dataItem) => {
                if (item.id == dataItem.id) {
                  return {
                    ...dataItem,
                    tag: [...dataItem.tag, tag]
                  }
                } else {
                  return dataItem
                }
              })
            })
          })
        }}
        updateDashboardDescription={(description) => {
          toast.promise(updateDashboardDescription(item.id, description), {
            pending: 'update description pending',
            success: 'update description success',
            error: 'update description error'
          }).then(() => {
            setDashboard((data) => {
              return data.map((dataItem) => {
                if (item.id == dataItem.id) {
                  return {
                    ...dataItem,
                    description: description
                  }
                } else {
                  return dataItem
                }
              })
            })
          })
        }}
      />)}
    </div>
  </div>
}

function DashboardDetail({
  dashboard,
  deleteDashboardData,
  deleteDashboardTag,
  addDashboardTag,
  updateDashboardDescription
}:{
  dashboard:DashboardDataWithChatHistory
  deleteDashboardData(): Promise<void>
  deleteDashboardTag(tag:string): void
  addDashboardTag(tag:string): void
  updateDashboardDescription(description:string): void
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [editingDescription, setEditingDescription] = useState<boolean>(false)
  const [newDescription, setNewDescription] = useState<string>(dashboard.description)
  const [addingTag, setAddingTag] = useState<boolean>(false)
  const [newTag, setNewTag] = useState<string>('')
  const router = useRouter()
  return <div className='p-4 border shadow-md rounded-md border-gray-300'>
    <div className='flex justify-between'>
      <div className='grow-0 basis-8/12'>
        <h2 className='text-xl flex gap-1 items-center flex-wrap'>
          <span>Tag:</span>
          {dashboard.tag.map((item, index) =>
            <div key={index} className="badge badge-soft badge-primary">
              {item}
              <button
                onClick={() => { deleteDashboardTag(item) }}
                className='btn btn-ghost btn-circle h-4 w-4 ml-1'
              >
                <RxCross2 />
              </button>
            </div>
          )}
          {addingTag ? (
            <div className="flex gap-1 items-center">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                className="input input-xs w-20"
                placeholder="New tag"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && newTag.trim()) {
                    addDashboardTag(newTag.trim())
                    setNewTag('')
                    setAddingTag(false)
                  } else if (e.key === 'Escape') {
                    setNewTag('')
                    setAddingTag(false)
                  }
                }}
                autoFocus
              />
              <button
                onClick={() => {
                  if (newTag.trim()) {
                    addDashboardTag(newTag.trim())
                    setNewTag('')
                    setAddingTag(false)
                  }
                }}
                className="btn btn-xs btn-success hover:btn-success-focus"
                title="保存标签"
              >
                <FaCheck className="w-3 h-3" />
              </button>
              <button
                onClick={() => {
                  setNewTag('')
                  setAddingTag(false)
                }}
                className="btn btn-xs btn-error hover:btn-error-focus"
                title="取消"
              >
                <FaTimes className="w-3 h-3" />
              </button>
            </div>
          ) : (
            <button
              onClick={() => setAddingTag(true)}
              className="btn btn-xs btn-ghost hover:bg-green-50 hover:text-green-600 transition-colors duration-200"
              title="添加标签"
            >
              <FaPlus className="w-3 h-3" />
            </button>
          )}
        </h2>
        <div className='flex items-center gap-2 mb-2'>
          {editingDescription ? (
            <div className="flex gap-1 items-start flex-1">
              <textarea
                value={newDescription}
                onChange={(e) => setNewDescription(e.target.value)}
                className="textarea textarea-sm flex-1 min-h-[60px] resize-y"
                placeholder="输入描述..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    updateDashboardDescription(newDescription)
                    setEditingDescription(false)
                  } else if (e.key === 'Escape') {
                    setNewDescription(dashboard.description)
                    setEditingDescription(false)
                  }
                }}
                autoFocus
              />
              <div className="flex flex-col gap-1">
                <button
                  onClick={() => {
                    updateDashboardDescription(newDescription)
                    setEditingDescription(false)
                  }}
                  className="btn btn-xs btn-success hover:btn-success-focus"
                  title="保存 (Enter)"
                >
                  <FaCheck className="w-3 h-3" />
                </button>
                <button
                  onClick={() => {
                    setNewDescription(dashboard.description)
                    setEditingDescription(false)
                  }}
                  className="btn btn-xs btn-error hover:btn-error-focus"
                  title="取消 (Esc)"
                >
                  <FaTimes className="w-3 h-3" />
                </button>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2 flex-1">
              <p className='text-sm text-gray-800 flex-1'>{dashboard.description}</p>
              <button
                onClick={() => setEditingDescription(true)}
                className="btn btn-xs btn-ghost hover:bg-blue-50 hover:text-blue-600"
                title="编辑描述"
              >
                <HiOutlinePencilAlt className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
        <span className='text-sm text-gray-400'>{dayjs(dashboard.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
        {dashboard.chat_history.map((item) => {
          return <div key={item.id} className='text-sm whitespace-pre-wrap'>{`[${dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}] ${item.role}:${item.content}`}</div>
        })}
      </div>
      <div className='flex gap-2'>
        <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={(e) => {
          setLoading(true)
          toast.promise(deleteDashboardData(), {
            pending:'delete pending',
            success:'delete success',
            error:'delete error'
          }).then(() => {
            router.refresh()
          }).finally(() => {
            setLoading(false)
          })
        }}>delete</button>
        <Link href={`/yuhe/user/chat/${dashboard.chat_id}#${dashboard?.chat_history[0]?.id ?? ''}`}><button className='btn btn-neutral'>link</button></Link>
      </div>
    </div>
  </div>
}